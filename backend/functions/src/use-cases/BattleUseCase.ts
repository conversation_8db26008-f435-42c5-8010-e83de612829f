import assert from "node:assert";
import { PopulatedDeck } from "../domain/entities/Deck";
import { PigeonGang } from "../domain/enums/PigeonGang";
import { TrainerRepository } from "../domain/repositories/TrainerRepository";
import { Stats } from "../domain/value-objects/Stats";

enum Ability {
    POISON = "POISON",
}

enum State {
    POISONED = "POISONED",
}

enum TopOrBottom {
    TOP = "top",
    BOTTOM = "bottom",
}

const BASE_DIGNITY_POINTS = 100;

type AttackOrDefenseLine = [FightingPigeon | null, FightingPigeon | null, FightingPigeon | null];

enum PositionCapacity {
    MID, // +20% Health
    LEFT, // 10% doublattack or dodge
    RIGHT, // Gives the Gang type to the two other pigeons
}

enum GangCapacity {
    RESURRECTION, // For a defender, gives a chance to resurrect. Cumulative with other same gang members. % chance and % of health restored.
    SNEAK_ATTACK, // For an attacker, gives a chance to deal damage to the defender's trainer. Cumulative with other same gang members. % chance and % of damage dealt.
    HASTE, // For an attacker, gives a chance to attack first. Attacking first gives a bit more damage. Cumulative with other same gang members. % chance.
    HEALING_AURA, // For a defender, gives a chance to heal other defending pigeons. Cumulative with other different gang members. % chance and % of health restored.
    WAR_LEADER, // Increases the stats of all other pigeons. Cumulative with other different gang members. % increase.
}

class FightingPigeon {
    stats: Stats;
    currentHealth: number;
    initialHealth: number;
    hasResurrected: boolean;
    states: State[];
    gangs: PigeonGang[];

    constructor({ computedStats, gangs }: { computedStats: Stats; gangs: PigeonGang[] }) {
        this.stats = computedStats;
        this.currentHealth = computedStats.health;
        this.initialHealth = computedStats.health;
        this.hasResurrected = false;
        this.states = [];
        this.gangs = gangs;
    }
}

enum BattlePhase {
    PRE_TURN,
    ATTACK,
    DEFENSE,
    POST_TURN,
}

interface BattleTurn {
    attacker: TopOrBottom;
    attackerPigeonIndex: 0 | 1 | 2;
    defenderPigeonIndex: 0 | 1 | 2 | null;
    pigeonDamage: number;
    trainerDamage: number;
    wasCritic: boolean;
    wasDodged: boolean;
    poisonDamage: number;
}

interface Battle {
    topTrainerDignityPoints: number;
    topAttack: AttackOrDefenseLine;
    topDefense: AttackOrDefenseLine;
    bottomTrainerDignityPoints: number;
    bottomAttack: AttackOrDefenseLine;
    bottomDefense: AttackOrDefenseLine;
    turns: BattleTurn[];
    winner: TopOrBottom;
}

export class BattleUseCase {
    constructor(private trainerRepo: TrainerRepository) {}

    async execute(topTrainerId: string, bottomTrainerId: string): Promise<void> {
        const topTrainer = await this.trainerRepo.getById(topTrainerId);
        const bottomTrainer = await this.trainerRepo.getById(bottomTrainerId);

        assert(topTrainer, "Trainer not found");
        assert(bottomTrainer, "Trainer not found");

        // const topDeck = await this.trainerRepo.getCurrentDeck(topTrainerId);
        // const bottomDeck = await this.trainerRepo.getCurrentDeck(bottomTrainerId);

        // assert(topDeck, "Deck not found");
        // assert(bottomDeck, "Deck not found");

        // const battle = this._createBattle(topDeck, bottomDeck);
        // const endBattle = this._simulateBattle(battle);

        // return newPigeon;
    }

    _createBattle(topDeck: PopulatedDeck, bottomDeck: PopulatedDeck): Battle {
        const [topAttack, topDefense] = this._createFightingPigeons(topDeck);
        const [bottomAttack, bottomDefense] = this._createFightingPigeons(bottomDeck);
        const battle: Battle = {
            topTrainerDignityPoints: BASE_DIGNITY_POINTS,
            topAttack,
            topDefense,
            bottomTrainerDignityPoints: BASE_DIGNITY_POINTS,
            bottomAttack,
            bottomDefense,
            turns: [],
            winner: TopOrBottom.TOP,
        };
        return battle;
    }

    _createFightingPigeons(deck: PopulatedDeck): [AttackOrDefenseLine, AttackOrDefenseLine] {
        const attack: AttackOrDefenseLine = [null, null, null];
        const defense: AttackOrDefenseLine = [null, null, null];
        const [leftAttackerPigeon, midAttackerPigeon, rightAttackerPigeon] = deck.attack;
        const [leftDefenderPigeon, midDefenderPigeon, rightDefenderPigeon] = deck.defense;

        const addedGang = rightAttackerPigeon?.gang;
        const midAttacker = new FightingPigeon({
            computedStats: { ...leftAttackerPigeon.totalStats, health: leftAttackerPigeon.totalStats.health * 1.2 },
            gangs: [leftAttackerPigeon.gang, addedGang],
        });

        [leftAttackerPigeon, midAttackerPigeon, rightAttackerPigeon].forEach((pigeon) => {
            if (pigeon.currentFatigue === 0) {
                attack.push(null);
                return;
            }
            const fightingPigeon: FightingPigeon = {
                gang: pigeon.gang,
                stats: pigeon.totalStats,
                auraPoints: pigeon.auraPoints,
                abilities: [],
                currentHealth: pigeon.totalStats.health,
                initialHealth: pigeon.totalStats.health,
                states: [],
            };
            attack.push(fightingPigeon);
        });
        deck.defense.forEach((pigeon) => {
            if (pigeon.currentFatigue === 0) {
                defense.push(null);
                return;
            }
            const fightingPigeon: FightingPigeon = {
                gang: pigeon.gang,
                stats: pigeon.totalStats,
                auraPoints: pigeon.auraPoints,
                abilities: [],
                currentHealth: pigeon.totalStats.health,
                initialHealth: pigeon.totalStats.health,
                states: [],
            };
            defense.push(fightingPigeon);
        });
        return [attack, defense];
    }

    /**
     *
     * This method will simulate the full battle starting from the initial decks.
     * It will modify the battle object and return it.
     */
    _simulateBattle(): void {
        // let turn = 0;
        // while (battle.topTrainerDignityPoints > 0 || battle.bottomTrainerDignityPoints > 0) {
        //     for (const attackingPosition of [0, 1, 2]) {
        //         const randomOrder = [TopOrBottom.TOP, TopOrBottom.BOTTOM].sort(() => Math.random() - 0.5);
        //         for (const currentAttacker of randomOrder) {
        //             const attack = currentAttacker === TopOrBottom.TOP ? battle.topAttack : battle.bottomAttack;
        //             const defense = currentAttacker === TopOrBottom.TOP ? battle.bottomDefense : battle.topDefense;
        //             const attackingPigeon = attack[attackingPosition];
        //             if (!attackingPigeon) {
        //                 continue;
        //             }
        //             const target = this._chooseTarget(attackingPosition, defense);
        //             if (target === 'trainer') {
        //                 const trainerDamage = attackingPigeon.stats.health;
        //                 if (currentAttacker === TopOrBottom.TOP) {
        //                     battle.bottomTrainerDignityPoints -= trainerDamage;
        //                 } else {
        //                     battle.topTrainerDignityPoints -= trainerDamage;
        //                 }
        //                 const turnResult: BattleTurn = {
        //                     attacker: currentAttacker,
        //                     attackerPigeonIndex: attackingPosition as 0 | 1 | 2,
        //                     defenderPigeonIndex: null,
        //                     pigeonDamage: 0,
        //                     trainerDamage,
        //                     wasCritic: false,
        //                     wasDodged: false,
        //                     poisonDamage: 0
        //                 };
        //                 battle.turns.push(turnResult);
        //                 turn++;
        //                 continue;
        //             }
        //             const defendingPigeon = target.target;;
        //             const damage = this._computeDamage(attackingPigeon, defendingPigeon);
        //         }
        //     }
        //     const turnResult = this._simulateTurn(battle);
        //     battle.turns.push(turnResult);
        //     if (turnResult.winner === 'top') {
        //         battle.bottomTrainerDignityPoints -= turnResult.trainerDamage;
        //     } else {
        //         battle.topTrainerDignityPoints -= turnResult.trainerDamage;
        //     }
        //     turn++;
        // }
        // battle.winner = battle.topTrainerDignityPoints > 0 ? 'top' : 'bottom';
        // return battle;
    }

    /**
     * Sélectionne le pigeon de défense cible en fonction de la position du pigeon attaquant.
     * La logique de priorité est la suivante :
     * - Si le pigeon attaquant est en position 0 (left), cible prioritaire = position 2 (right), puis 1, puis 0.
     * - Si en position 1 (mid), cible prioritaire = position 1, puis 0, puis 2.
     * - Si en position 2 (right), cible prioritaire = position 0 (left), puis 1, puis 2.
     */
    // private _chooseTarget(
    //     attackerPosition: number,
    //     defense: AttackOrDefenseLine
    // ): {target: FightingPigeon, position: 0 | 1 | 2} | 'trainer' {
    //     let priorityOrder: number[];
    //     if (attackerPosition === 0) {
    //         priorityOrder = [2, 1, 0];
    //     } else if (attackerPosition === 1) {
    //         priorityOrder = [1, 0, 2];
    //     } else {
    //         priorityOrder = [0, 1, 2];
    //     }

    //     for (const pos of priorityOrder) {
    //         const target = defense[pos];
    //         if (target && target.currentHealth > 0) {
    //             return {target, position: pos as 0 | 1 | 2};
    //         }
    //     }
    //     return 'trainer';
    // }

    // private _computeDamage(attacker: FightingPigeon, defender: FightingPigeon): void {
    //     const theoreticalDamage = attacker.stats.strength;
    //     const defense = defender.stats.strength;
    //     const isCritic = Math.random() < 0.1;
    //     const deliveredDamage = isCritic ? theoreticalDamage * 1.5 : theoreticalDamage;
    // }
}
