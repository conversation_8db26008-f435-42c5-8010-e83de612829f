export class PingExternalServiceUseCase {
    readonly whatsappServerUrl = "https://whatsapp-server.com";

    async execute(data: Record<string, unknown>): Promise<void> {
        await fetch(this.whatsappServerUrl, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(data),
        }).catch((error) => {
            console.error("Error pinging external service:", error);
        });
    }
}
