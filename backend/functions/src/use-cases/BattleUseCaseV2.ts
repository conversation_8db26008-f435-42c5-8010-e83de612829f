// BattleUseCase.ts
// ------------------------------------------------------------
// A self-contained battle engine for the Pigeon Game.
// Implements the rules described by the user (July 2025).
// ------------------------------------------------------------

import { PigeonGang } from "../domain/enums/PigeonGang";
import { Stats } from "../domain/value-objects/Stats";

// -------------------------------
// ENUMS & BASIC TYPE DEFINITIONS
// -------------------------------
export enum Position {
    LEFT = "LEFT",
    MID = "MID",
    RIGHT = "RIGHT",
}

export interface PigeonInit {
    id: string;
    baseStats: Stats;
    gang: PigeonGang;
    position: Position;
}

// ---------------------------------
// RUNTIME MODELS (MUTABLE IN-FIGHT)
// ---------------------------------
export class FightingPigeon {
    readonly id: string;
    readonly gang: PigeonGang;
    readonly position: Position;
    readonly baseStats: Stats;

    /** Effective stats after applying positional & team bonuses. */
    stats: Stats;
    /** Remaining lifepoints. 0 ⇒ KO */
    hp: number;
    /** Whether this pigeon has already transferred some of its HP to its trainer. */
    transferred = false;
    /** Each MAISON_ROSSINI pigeon can resurrect once. */
    private resurrected = false;

    constructor(init: PigeonInit, boostedStats: Stats) {
        this.id = init.id;
        this.gang = init.gang;
        this.position = init.position;
        this.baseStats = { ...init.baseStats };
        this.stats = { ...boostedStats };
        this.hp = this.stats.health; // lifepoints when acting as defender
    }

    get alive(): boolean {
        return this.hp > 0;
    }

    /** Damage reducer when acting as defender. */
    get paradeFactor(): number {
        return 1 - this.stats.agility / 100; // agility is % reduction (def).
    }

    /** Crit chance when acting as attacker. */
    get critChance(): number {
        return this.stats.agility / 100; // agility is % crit chance (atk).
    }

    takeDamage(amount: number): void {
        this.hp = Math.max(0, this.hp - amount);
    }

    /** Attempt a MAISON_ROSSINI resurrection (once per pigeon). */
    tryResurrect(chance: number, percentHeal: number): boolean {
        if (this.resurrected || this.gang !== PigeonGang.MAISON_ROSSINI) return false;
        if (Math.random() < chance) {
            this.hp = Math.floor(this.stats.health * percentHeal);
            this.resurrected = true;
            return true;
        }
        return false;
    }
}

// ---------------------------------
// TRAINER & TEAM UTILITIES
// ---------------------------------
export interface TrainerInit {
    id: string;
    dignityPoints: number; // starting trainer HP
    pigeons: PigeonInit[]; // exactly 6 pigeons (first 3 attack, last 3 defend)
}

export class Trainer {
    readonly id: string;
    dignity: number;
    /** First 3 are attack line (LEFT, MID, RIGHT) */
    attackLine: FightingPigeon[];
    /** Last 3 are defense line (LEFT, MID, RIGHT) */
    defenseLine: FightingPigeon[];

    /** Number of pigeons for each gang (for stacking computations). */
    readonly gangCounts: Map<PigeonGang, number>;

    constructor(init: TrainerInit, applyPositionalAndGangBonuses: (p: PigeonInit, all: PigeonInit[]) => Stats) {
        if (init.pigeons.length !== 6) {
            throw new Error("A trainer must supply exactly 6 pigeons (3 attackers + 3 defenders)");
        }
        this.id = init.id;
        this.dignity = init.dignityPoints;

        this.gangCounts = new Map<PigeonGang, number>();
        init.pigeons.forEach((p) => {
            this.gangCounts.set(
                p.gang,
                (this.gangCounts.get(p.gang) ?? 0) +
                    1 +
                    (p.position === Position.RIGHT &&
                    init.pigeons.find((q) => q.position === Position.MID && q.gang !== p.gang)
                        ? 1
                        : 0),
            );
        });

        const enriched = init.pigeons.map((p) => new FightingPigeon(p, applyPositionalAndGangBonuses(p, init.pigeons)));

        this.attackLine = enriched.slice(0, 3);
        this.defenseLine = enriched.slice(3);
    }

    /** Check if any defender is still alive. */
    get hasDefenders(): boolean {
        return this.defenseLine.some((p) => p.alive);
    }

    /** Get defender target based on mirror logic for a given attacker position. */
    getDefenderTarget(pos: Position): FightingPigeon | undefined {
        // Mirror: attacker LEFT targets opponent RIGHT first, MID→MID, RIGHT→LEFT.
        const order: Position[] =
            pos === Position.LEFT
                ? [Position.RIGHT, Position.MID, Position.LEFT]
                : pos === Position.MID
                  ? [Position.MID, Position.LEFT, Position.RIGHT]
                  : /* pos === RIGHT */ [Position.LEFT, Position.MID, Position.RIGHT];

        for (const p of order) {
            const index = p === Position.LEFT ? 0 : p === Position.MID ? 1 : 2;
            const candidate = this.defenseLine[index];
            if (candidate && candidate.alive) return candidate;
        }
        return undefined;
    }

    /** Number of different gangs represented in the whole 6-pigeon roster. */
    get diversity(): number {
        return this.gangCounts.size;
    }
}

// ------------------------
// CONSTANTS FOR BALANCING
// ------------------------
// Positional bonus multipliers.
const POSITIONAL_BONUS = {
    [Position.LEFT]: { agility: 0.2, health: 0, strength: 0 },
    [Position.MID]: { health: 0.2, agility: 0, strength: 0 },
    [Position.RIGHT]: { agility: 0, health: 0, strength: 0 }, // right has special gang transfer handled elsewhere
} as const;

const STARTER_ATTACK_BONUS = 0.05; // starting first gives +5% damage

// PigeonGang ability tuning – tweak as desired
const ROSSINI_BASE_CHANCE = 0.15; // base resurrection chance per Rossini pigeon
const ROSSINI_HEAL = 0.5; // % health restored when resurrected

const CRUMBS_BASE_CHANCE = 0.2; // base % chance to also hit trainer
const CRUMBS_EXTRA_DMG = 0.1; // extra dmg to trainer as % of dealt dmg

const CONTI_TURN_INIT_BONUS = 0.1; // each Conti pigeon adds 10% chance to start turn

const SYNDICAT_BASE_CHANCE = 0.1; // base heal chance
const SYNDICAT_HEAL_PERC = 0.1; // heal % of max HP

const OBNI_BOOST = 0.05; // each Obni boosts others' stats by +5%

// --------------
// BATTLE ENGINE
// --------------
export class BattleUseCase {
    private readonly trainerTop: Trainer;
    private readonly trainerBottom: Trainer;

    /**
     * @param trainerA The trainer shown on the top (first in logs)
     * @param trainerB The trainer shown on the bottom (second in logs)
     */
    constructor(trainerA: TrainerInit, trainerB: TrainerInit) {
        // Apply positional & gang bonuses lazily via helper
        const applyBonuses = (p: PigeonInit, roster: PigeonInit[]): Stats => {
            // Start with base stats
            let { health, strength, agility } = p.baseStats;

            // 1) Positional bonuses
            const posBonus = POSITIONAL_BONUS[p.position];
            health *= 1 + posBonus.health;
            strength *= 1 + posBonus.strength;
            agility *= 1 + posBonus.agility;

            // 2) OBNI global buff (affects OTHER pigeons)
            const obniCount = roster.filter((r) => r.gang === PigeonGang.OBNI).length;
            if (p.gang !== PigeonGang.OBNI && obniCount > 0) {
                const factor = 1 + obniCount * OBNI_BOOST;
                health *= factor;
                strength *= factor;
                agility *= factor;
            }

            // 3) Right-gang transfer → if this pigeon is MID and right pigeon exists
            if (p.position === Position.MID) {
                const right = roster.find((r) => r.position === Position.RIGHT);
                if (right) {
                    // Mid pigeon gains second gang; for stacking we simulate by increasing counts later.
                    // We keep gang unchanged but BattleUseCase will adjust counts when needed.
                }
            }

            return { health, strength, agility };
        };

        this.trainerTop = new Trainer(trainerA, applyBonuses);
        this.trainerBottom = new Trainer(trainerB, applyBonuses);
    }

    /**
     * Runs the full simulation, returning the winning trainer ID and a chronologically
     * ordered log of events (for UI / replay purposes).
     */
    public simulate(): { winner: string; turns: TurnLog[] } {
        const logs: TurnLog[] = [];
        let turn = 1;

        while (this.trainerTop.dignity > 0 && this.trainerBottom.dignity > 0) {
            logs.push(this.executeTurn(turn));
            turn += 1;

            if (turn > 250) {
                // safety net – should never happen.
                break;
            }
        }

        const winner =
            this.trainerTop.dignity <= 0
                ? this.trainerBottom.id
                : this.trainerBottom.dignity <= 0
                  ? this.trainerTop.id
                  : this.trainerTop.dignity > this.trainerBottom.dignity
                    ? this.trainerTop.id
                    : this.trainerBottom.id;

        return { winner, turns: logs };
    }

    // --------------------
    // INTERNAL TURN LOGIC
    // --------------------
    private executeTurn(turnNumber: number): TurnLog {
        const turnLog: TurnLog = { turn: turnNumber, phases: [] };

        // 1. LEFT column phase
        this.columnPhase(Position.LEFT, turnLog);
        // 2. MID column phase
        this.columnPhase(Position.MID, turnLog);
        // 3. RIGHT column phase
        this.columnPhase(Position.RIGHT, turnLog);

        // 4. End-of-turn abilities (Le Syndicat healing)
        this.handleSyndicatHeal(this.trainerTop, turnLog);
        this.handleSyndicatHeal(this.trainerBottom, turnLog);

        return turnLog;
    }

    /** Execute one column (LEFT, MID, RIGHT) attack sequence. */
    private columnPhase(position: Position, turnLog: TurnLog): void {
        const [first, second] = this.determineInitiative(position);

        this.pigeonAttack(first, position, second, turnLog);
        if (this.isBattleOver()) return;
        this.pigeonAttack(second, position, first, turnLog);
    }

    /** Decide which trainer attacks first in the specified column. */
    private determineInitiative(position: Position): [Trainer, Trainer] {
        const [topChance, bottomChance] = [
            this.initiativeChance(this.trainerTop, position),
            this.initiativeChance(this.trainerBottom, position),
        ];

        const roll = Math.random() * (topChance + bottomChance); // The higher bottomChance, the more likely it is to roll above topChance.
        const topStarts = roll < topChance;

        return topStarts ? [this.trainerTop, this.trainerBottom] : [this.trainerBottom, this.trainerTop];
    }

    /**
     * Base initiative is average agility of attack line + MAISON_CONTI bonus (+ flat 1 to avoid 0).
     */
    private initiativeChance(t: Trainer, position: Position): number {
        const attacker = t.attackLine[position === Position.LEFT ? 0 : position === Position.MID ? 1 : 2]; // Select pigeon based on position
        const base = attacker.stats.agility;
        const contiCount = t.gangCounts.get(PigeonGang.MAISON_CONTI) ?? 0;
        return 1 + base + contiCount * CONTI_TURN_INIT_BONUS * 100; // convert to similar scale
    }

    private pigeonAttack(
        attackerTrainer: Trainer,
        position: Position,
        opponentTrainer: Trainer,
        turnLog: TurnLog,
    ): void {
        const attacker = attackerTrainer.attackLine[position === Position.LEFT ? 0 : position === Position.MID ? 1 : 2];

        if (!opponentTrainer.hasDefenders) {
            // Attack trainer directly – damage = attacker's health stat (could crit)
            let dmg = attacker.stats.health;
            if (Math.random() < attacker.critChance) dmg *= 2;
            opponentTrainer.dignity -= Math.floor(dmg);

            turnLog.phases.push({
                actor: attackerTrainer.id,
                pigeonId: attacker.id,
                action: `strikes trainer for ${Math.floor(dmg)}`,
            });
            return;
        }

        const target = opponentTrainer.getDefenderTarget(position); // Only returns alive pigeons
        if (!target) return; // should not happen
        const dmg = this.computeAttackDamage(attacker, attackerTrainer === this.determineInitiative(position)[0]);
        const effective = Math.floor(dmg * target.paradeFactor);
        target.takeDamage(effective);

        turnLog.phases.push({
            actor: attackerTrainer.id,
            pigeonId: attacker.id,
            action: `hits ${target.id} for ${effective}`,
        });

        // LES_CRUMBS chance to also damage trainer
        if (attacker.gang === PigeonGang.LES_CRUMBS) {
            const crumbsCount = attackerTrainer.gangCounts.get(PigeonGang.LES_CRUMBS) ?? 0;
            const chance = CRUMBS_BASE_CHANCE * crumbsCount;
            if (Math.random() < chance) {
                const bonus = effective * (CRUMBS_EXTRA_DMG + 0.05 * (crumbsCount - 1));
                opponentTrainer.dignity -= Math.floor(bonus);
                turnLog.phases.push({
                    actor: attackerTrainer.id,
                    pigeonId: attacker.id,
                    action: `crumb-splash hits trainer for ${Math.floor(bonus)}`,
                });
            }
        }

        // Defender death effects (strength transfer, resurrection)
        if (!target.alive) {
            // Transfer lifepoints equal to defender.strength to its trainer (heal dignity)
            if (!target.transferred) {
                // <-- replaced previous single-line transfer
                opponentTrainer.dignity += target.stats.strength;
                target.transferred = true;
                turnLog.phases.push({
                    actor: opponentTrainer.id,
                    pigeonId: target.id,
                    action: `transfers ${target.stats.strength} dignity on death`,
                });
            }

            // MAISON_ROSSINI resurrection attempt
            const rossiniCount = opponentTrainer.gangCounts.get(PigeonGang.MAISON_ROSSINI) ?? 0;
            if (rossiniCount > 0) {
                const chance = ROSSINI_BASE_CHANCE * rossiniCount;
                const healed = ROSSINI_HEAL + 0.1 * (rossiniCount - 1); // small scaling
                const resurrected = target.tryResurrect(chance, healed);
                if (resurrected) {
                    turnLog.phases.push({
                        actor: opponentTrainer.id,
                        pigeonId: target.id,
                        action: `resurrects with ${Math.floor(target.hp)} HP`,
                    });
                }
            }
        }
    }

    /** Computes base damage from an attacking pigeon. */
    private computeAttackDamage(attacker: FightingPigeon, startedFirst: boolean): number {
        let dmg = attacker.stats.strength;
        if (Math.random() < attacker.critChance) dmg *= 2;
        if (startedFirst) dmg *= 1 + STARTER_ATTACK_BONUS; // starting first bonus
        return Math.floor(dmg);
    }

    private handleSyndicatHeal(trainer: Trainer, turnLog: TurnLog): void {
        const distinctGangs = trainer.diversity;
        const chance = SYNDICAT_BASE_CHANCE * distinctGangs;
        if (Math.random() < chance) {
            const healPerc = SYNDICAT_HEAL_PERC + 0.05 * (distinctGangs - 1);
            trainer.defenseLine.forEach((p) => {
                if (p.alive) {
                    const healed = Math.floor(p.stats.health * healPerc);
                    p.hp = Math.min(p.stats.health, p.hp + healed);
                    turnLog.phases.push({ actor: trainer.id, pigeonId: p.id, action: `heals ${healed}` });
                }
            });
        }
    }

    private isBattleOver(): boolean {
        return this.trainerTop.dignity <= 0 || this.trainerBottom.dignity <= 0;
    }
}

// -----------------------------------
// LOG STRUCTURES FOR UI CONSUMPTION
// -----------------------------------
export interface ActionLog {
    actor: string; // trainer ID (top/bottom)
    pigeonId: string; // acting pigeon
    action: string; // descriptive string – keep short for log UI
}

export interface TurnLog {
    turn: number;
    phases: ActionLog[];
}

// -------------------
// USAGE EXAMPLE (TS)
// -------------------
/*
const battle = new BattleUseCase(trainerAInit, trainerBInit);
const result = battle.simulate();
console.log(`Winner is ${result.winner}`, result.turns);
*/
