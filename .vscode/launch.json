{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "Debug Current File",
      "skipFiles": ["<node_internals>/**"],
      "program": "${file}",
      "preLaunchTask": "tsc: build - functions",
      "outFiles": ["${workspaceFolder}/backend/functions/lib/**/*.js"]
    },
    {
      "type": "pwa-node",
      "request": "launch",
      "name": "Debug TypeScript File",
      "program": "${file}",
      "runtimeArgs": ["-r", "ts-node/register"],
      "cwd": "${workspaceFolder}",
      "env": {
        "TS_NODE_PROJECT": "${workspaceFolder}/tsconfig.json"
      },
      "skipFiles": ["<node_internals>/**"],
      "resolveSourceMapLocations": [
        "${workspaceFolder}/**",
        "!**/node_modules/**"
      ]
    },
    {
      "type": "node",
      "nodeVersionHint": 22.15,
      "request": "launch",
      "runtimeVersion": "22.15.0",
      "runtimeExecutable": "npm",
      "name": "Start Frontend",
      "runtimeArgs": ["run", "start"],
      "cwd": "${workspaceFolder}/frontend/desktop"
    },
  ]
}
